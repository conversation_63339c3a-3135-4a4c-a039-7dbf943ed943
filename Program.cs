using iText.Bouncycastle.Crypto;
using iText.Bouncycastle.X509;
using iText.Commons.Bouncycastle.Cert;
using iText.Commons.Bouncycastle.Crypto;
using iText.Forms.Form.Element;
using iText.IO.Image;
using iText.IO.Source;
using iText.Kernel.Crypto;
using iText.Kernel.Geom;
using iText.Kernel.Pdf;
using iText.Signatures;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Utilities.IO.Pem;
using Org.BouncyCastle.X509;
using PdfSigner;
using System.Text;
using System.Text.Json;
using Path = System.IO.Path;

namespace PdfSigner
{
    // Request and Response models
    public record SignPdfRequest(string Base64Pdf, string Base64Sig);
    public record SignPdfResponse(string Base64SignedPdf, bool IsSuccess, string Message);
    public class KuknosDigitalSignContextV1
    {
        public Guid? OrderTrackingCode { get; set; }
        public long DigitalSignId { get; set; }
        public int UserId { get; set; }
        public int Status { get; set; }
        public string FullNameFa { get; set; }
        public string Address { get; set; }

        public string Mobile { get; set; }
        public string NationalCode { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Email { get; set; }

        /// <summary>
        /// format  yyyy/mm/dd
        /// </summary>
        public string BirthDate { get; set; }
        public string NationalCardSeries { get; set; }
        public string PostalCode { get; set; }
        public string FirstNameEn { get; set; }
        public string LastNameEn { get; set; }
        public string FirstNameFa { get; set; }
        public string LastNameFa { get; set; }
        public string FatherNameFa { get; set; }
        public bool IsMale { get; set; }

        public string PublicKey { get; set; }
        public string PrivateKey { get; set; }
        public string UserMobileSignature { get; set; }

        public string CertificateSigningRequest { get; set; }
        public string CertificateTrackingCode { get; set; }
        public string Certificate { get; set; }

    }
    public static class FileHelper
    {
        public const string SignData = """{"City":"Tehran","Email":"<EMAIL>","IsMale":true,"Mobile":"09371347248","Status":50,"UserId":168590,"Address":"تهران٬ خ سوم٬ پلاک دوم","Province":"THR","BirthDate":"1364/06/26","PublicKey":"MIIBCgKCAQEA6fA+M8aKRevEeMxPU7A96McjWdStubNCl0f9COucx2uVUYPkOnlthgwQ15M40RJvixeqA40rqh5S4MH1cVNQedGZVinFZyuNuUqoNf/kosJD/FOMHJX409LHHgG62eS1VRe8wbF50gWd95C82NPf/jUT8RwocrYMU881YBWm+KnlnCe8jtmwA5LeGljD/A0w2SKZHk/08RHnaPVOocBn8wftHJT/x/4lkSs27DoN8R+ZgUM2wyuV9zPDNGdD2kDS7z3rLHmWTjjwtT4cUARJdi4cg9vZTrPMMN8uj1EUGLJGRfZ+1tEkBPL2t4T/9ArISkyT2fSC31KfIdmGItQjdQIDAQAB","FullNameFa":"حميد رحماني مقدم","LastNameEn":"Rahmani","LastNameFa":"رحماني مقدم","PostalCode":"9449499444","PrivateKey":"MIIEogIBAAKCAQEA6fA+M8aKRevEeMxPU7A96McjWdStubNCl0f9COucx2uVUYPkOnlthgwQ15M40RJvixeqA40rqh5S4MH1cVNQedGZVinFZyuNuUqoNf/kosJD/FOMHJX409LHHgG62eS1VRe8wbF50gWd95C82NPf/jUT8RwocrYMU881YBWm+KnlnCe8jtmwA5LeGljD/A0w2SKZHk/08RHnaPVOocBn8wftHJT/x/4lkSs27DoN8R+ZgUM2wyuV9zPDNGdD2kDS7z3rLHmWTjjwtT4cUARJdi4cg9vZTrPMMN8uj1EUGLJGRfZ+1tEkBPL2t4T/9ArISkyT2fSC31KfIdmGItQjdQIDAQABAoIBAA60bryDbNqjTMjJEu1wRRv2NIcngroLBhMLT+D5KnQwk4S5tSVlz5ge9woN84LU1VUZajLp80YwvMXq+zoh4guePLS5UHLBPpnYkwU97gm7C1n3iHWdibQK6kA9HJkosqtEIDXOqZkliD3xB6Xbf5dzj6rWucYoIlM8UZ8WDVZQ/NYNmx/vGgqytMCd54XqsyiwywsAdW1TtUApO6W6vyM2YIhlh4xX4WRUV3Q8VA8jgzKjwwusaevXNQVgyPVeUn8Q64BP+jBdFK0Y57L7bdiqxsr3OWbyB5Q1vUaKaGG0wGa18scYsHR7WGrT7ZmbX0n43zrz7YngfPW1iSm1i0sCgYEA/30um+2vw/iSYu3oaA+xBgSqiuBpUQeuMKiOCYKzQeW+AOmQ5ViZq7iLLkhaTDcQ9/5Ajz+rgONfEJGW39pOkTq7W6h2t4B3uu4Rw9gpWh8l4C7MRCB8rkDEbdtFKm57jT6gZdmDhdH07L856K35PEelK5TNjQbgWvQZwicC8C8CgYEA6mgGwcPbV9ffnD6zB8LVl0bwsVGplrgxzs8p2mRo+U+oNcqMiuw2SeuoV+Y5yMg4rl2yGtsgCKIJxuO0MAnPRz4puOt7PfyAPJJYoqyrUAd7HEBNy+saxyu2HMBe4Q0LbuTTMXCssbuQpELn+gCAKzNVVZNpfEYgvobk53vB+ZsCgYA+9ITIrYATJSxFLmSHR96SIhI4GGY7Lk5Q9SfS1h7/zPHN3b/zk6qCMv4w0GlXwHXw9fjIk6LaJqzlM0rfLDcR+Ev0oQVn4EMgj8tW2+wMpgI6RK3W176BXbpJa6+WbhUJz/XMQxXyorLcp55IZJ42qVzc6VThPGGKZ/305pIFxwKBgBp3tIuced/pWC5kRgLSVRvHvtXGysdh9P3NFoS6YcAE5pFsVYI6OccO/Osv68ZoFT5bBglSRMO0yDXU8DucX/NDaaKftELDiBdOzwNPS+i1b4k5GY+09EkY/m8wqYXv2yKiY9ZpSpam/yYNB/SKqe421aPcCUUnBft9b8tThguRAoGAPWtPUgB/cQ04S3MuIKLIYoqwIz/vcnb4Rnhj1FjoLLJZHQVbsQncVR/skukswm4uayR3nr+rzFuhFwDFncP8Gic8uFOZnWyDLn4szBZr9ERVSfHZ5T+MPsbljvf3EcpZnqHMLR/aWbwv1prRtrBpPQEQds7eU6KJoMB3RCxNIJ4=","Certificate":"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","FirstNameEn":"Hamid","FirstNameFa":"حميد","FatherNameFa":"محمدحسين","NationalCode":"0079325149","DigitalSignId":45,"OrderTrackingCode":null,"NationalCardSeries":"2r12747016","UserMobileSignature":"DOHWODY2+61ypcTaF6vlUXP1O8f7Ke5B38+KYW0L688cyBIPDVuL6ccrxtdIhxO8hf6whiC6254mMFFAu15gUdK9+t6cIM0exERgSw3hp4ItjJvuYIb1N2GLS/zD7/iIJBj62UoDGBr2mz5FKn9ceD1yrKlY1Z6w40TjfoieO1qcZSqud/OJJtH4E2dMYSfcXQUm/XAPW6jbcDLFUdg4KY/h0JAhnpoDAosJiuK/fHvfpanzdRYQ448JI4TsSoGijmbJjwy1Ip92NUX3dwsS8e05r6Qrz44qD+zY4lzFQWsECT+6x2NHQFw2x53rnwdiGWvUgsQF2ncUMPlCxnjx1g==","CertificateTrackingCode":"39381aab-8aab-4e1b-8f11-ab110da9dffd","CertificateSigningRequest":"MIIEnTCCA4UCAQAwggJWMRYwFAYDVQQUEw0rOTg5MzcxMzQ3MjQ4MRAwDgYDVQQEEwdSYWhtYW5pMQ4wDAYDVQQqEwVIYW1pZDETMBEGA1UEBRMKMDA3OTMyNTE0OTEVMBMGA1UECxMMVW5hZmZpbGlhdGVkMRUwEwYDVQQKEwxVbmFmZmlsaWF0ZWQxIzAhBgkqhkiG9w0BCQEWFHJhaG1hbmkuZWNAZ21haWwuY29tMQwwCgYDVQQIEwNUSFIxDzANBgNVBAcTBlRlaHJhbjELMAkGA1UEBhMCSVIxggGEMIIBgAYDVQQDDIIBd9it2YXZitivINix2K3Zhdin2YbZiiDZhdmC2K/ZhSBET0hXT0RZMis2MXlwY1RhRjZ2bFVYUDFPOGY3S2U1QjM4K0tZVzBMNjg4Y3lCSVBEVnVMNmNjcnh0ZEloeE84aGY2d2hpQzYyNTRtTUZGQXUxNWdVZEs5K3Q2Y0lNMGV4RVJnU3czaHA0SXRqSnZ1WUliMU4yR0xTL3pENy9pSUpCajYyVW9ER0JyMm16NUZLbjljZUQxeXJLbFkxWjZ3NDBUamZvaWVPMXFjWlNxdWQvT0pKdEg0RTJkTVlTZmNYUVVtL1hBUFc2amJjRExGVWRnNEtZL2gwSkFobnBvREFvc0ppdUsvZkh2ZnBhbnpkUllRNDQ4Skk0VHNTb0dpam1iSmp3eTFJcDkyTlVYM2R3c1M4ZTA1cjZRcno0NHFEK3pZNGx6RlFXc0VDVCs2eDJOSFFGdzJ4NTNybndkaUdXdlVnc1FGMm5jVU1QbEN4bmp4MWc9PTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOnwPjPGikXrxHjMT1OwPejHI1nUrbmzQpdH/QjrnMdrlVGD5Dp5bYYMENeTONESb4sXqgONK6oeUuDB9XFTUHnRmVYpxWcrjblKqDX/5KLCQ/xTjByV+NPSxx4ButnktVUXvMGxedIFnfeQvNjT3/41E/EcKHK2DFPPNWAVpvip5ZwnvI7ZsAOS3hpYw/wNMNkimR5P9PER52j1TqHAZ/MH7RyU/8f+JZErNuw6DfEfmYFDNsMrlfczwzRnQ9pA0u896yx5lk448LU+HFAESXYuHIPb2U6zzDDfLo9RFBiyRkX2ftbRJATy9reE//QKyEpMk9n0gt9SnyHZhiLUI3UCAwEAAaAAMA0GCSqGSIb3DQEBCwUAA4IBAQA6NVLYaEDtnEaxAQHRlVrztODkm/P4go0kPacOO85bDysYNtoUCMjKAB310EOBuFjDrAC8MinzeCGjplpU4yAdkQaDjB8Zn/lvmvxPClIYBq49ygLjFD2eS+3R2/micSMVj5txUEWNb2KZAtz831x2fl5ZI/4WGXeq1US/QYe1fR4HLB0n7eTdFnLEC/nTrVY0mEjn/+ouNjT6qwTTh0QXOa+ncHKKVZBzM0JrH27c+H2WZmSbDB9sv19E583OHntqiMnLMjdo4letcb9YgcU5YV1Q+wMRAs3UexvLKb9ia8I9U7sLOISbzBM9njuhWZB7n+8mA92m6f1tSAw2Bi+i"}""";
        public static async Task<byte[]> ToByteArrayAsync(this Stream stream)
        {
            switch (stream)
            {
                case MemoryStream mem:
                    return mem.ToArray();
                default:
                    using (var m = new MemoryStream())
                    {
                        await stream.CopyToAsync(m);
                        return m.ToArray();
                    }
            }
        }
    }
}

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Add services to the container.
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();

        var app = builder.Build();

        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();

        app.MapPost("/sign-pdf", async ([FromBody] SignPdfRequest request) =>
        {
            try
            {
                // Validate input
                if (string.IsNullOrEmpty(request.Base64Pdf))
                {
                    return Results.BadRequest(new SignPdfResponse("", false, "PDF data is required"));
                }
                var sigData = JsonSerializer.Deserialize<KuknosDigitalSignContextV1>(FileHelper.SignData)
                    ?? throw new NullReferenceException();
                // Convert base64 to byte array
                byte[] pdfBytes;
                try
                {
                    pdfBytes = Convert.FromBase64String(request.Base64Pdf);
                }
                catch (FormatException)
                {
                    return Results.BadRequest(new SignPdfResponse("", false, "Invalid base64 string"));
                }

                // Sign the PDF
                byte[] signedPdfBytes;
                // Create a temporary file for the signed PDF
                var tempFile = Path.GetTempFileName();

                // Sign the PDF
                using (var pdfReader = new MemoryStream(pdfBytes))
                await using (var fileStream = new FileStream(tempFile, FileMode.Create))
                {
                    await using var byteArrayOutputStream = new ByteArrayOutputStream();

                    byte[] fileBytes;
                    try
                    {
                        fileBytes = Convert.FromBase64String(request.Base64Sig);
                    }
                    catch (FormatException)
                    {
                        return Results.BadRequest(new SignPdfResponse("", false, "Invalid base64 string"));
                    }
                    var imgData = ImageDataFactory.Create(fileBytes);

                    // sign pdf
                    SignDocumentSignature(pdfReader, byteArrayOutputStream, imgData, sigData.PrivateKey, sigData.Certificate);

                    signedPdfBytes = await byteArrayOutputStream.ToByteArrayAsync();

                }

                //// Read the signed PDF into memory
                //signedPdfBytes = await File.ReadAllBytesAsync(tempFile);

                Console.WriteLine(tempFile);
                // Convert signed PDF to base64
                var base64SignedPdf = Convert.ToBase64String(signedPdfBytes);

                return Results.Ok(new SignPdfResponse(base64SignedPdf, true, "PDF signed successfully"));
            }
            catch (Exception ex)
            {
                return Results.Problem(
                    detail: ex.Message,
                    statusCode: StatusCodes.Status500InternalServerError,
                    title: "Error signing PDF"
                );
            }
        })
        .WithName("SignPdf")
        .WithOpenApi();

        app.Run();
    }

    private static void SignDocumentSignature(Stream rawContract, ByteArrayOutputStream byteArrayOutputStream,
        ImageData imageData, string pkey, string certStr)
    {
        var pdfSigner = new iText.Signatures.PdfSigner(new PdfReader(rawContract),
            new PdfWriter(byteArrayOutputStream),
            new StampingProperties());

        var signerProperties = new SignerProperties();
        signerProperties.SetCertificationLevel(AccessPermissions.NO_CHANGES_PERMITTED);

        // Set the name indicating the field to be signed.
        // The field can already be present in the document but shall not be signed
        signerProperties.SetFieldName("signature");

        pdfSigner.SetSignerProperties(signerProperties);

        // If you create new signature field (or use SetFieldName(System.String) with
        // the name that doesn't exist in the document or don't specify it at all) then
        // the signature is invisible by default.
        var appearance =
            new SignatureFieldAppearance(SignerProperties.IGNORED_ID).SetContent(imageData);

        var document = pdfSigner.GetDocument();

        signerProperties.SetPageNumber(document.GetNumberOfPages())
            .SetPageRect(new Rectangle(150, 150, 150, 150))
            .SetSignatureAppearance(appearance);

        IExternalSignature pks = GetPrivateKeySignature(pkey);
        var chain = LoadCertificateChainFromBase64(certStr);
        var ocspClient = new OcspClientBouncyCastle();
        var crlClients = new List<ICrlClient>([new CrlClientOnline()]);

        // Sign the document using the detached mode, CMS or CAdES equivalent.
        // This method closes the underlying pdf document, so the instance
        // of PdfSigner cannot be used after this method call
        pdfSigner.SignDetached(pks, chain, crlClients, ocspClient, null, 0,
            iText.Signatures.PdfSigner.CryptoStandard.CMS);
    }
    private static PrivateKeySignature GetPrivateKeySignature(string pkey)
    {
        IPrivateKey pk = new PrivateKeyBC(ReadPrivateKey(pkey));
        return new PrivateKeySignature(pk, DigestAlgorithms.SHA256);
    }

    private static AsymmetricKeyParameter ReadPrivateKey(string privateKey)
    {
        var pkPem = GetPkcs8PrivateKeyFormat(privateKey);

        using var sr = new StringReader(pkPem);

        var pemReader = new PemReader(sr);
        var pemObject = pemReader.ReadPemObject();
        var rsa = RsaPrivateKeyStructure.GetInstance(pemObject.Content);

        return new RsaPrivateCrtKeyParameters(rsa.Modulus,
            rsa.PublicExponent, rsa.PrivateExponent,
            rsa.Prime1, rsa.Prime2, rsa.Exponent1,
            rsa.Exponent2, rsa.Coefficient);
    }

    private static string GetPkcs8PrivateKeyFormat(string privateKey)
    {
        var sb = new StringBuilder();
        sb.AppendLine("-----BEGIN PRIVATE KEY-----");
        AppendBody(sb, privateKey);
        sb.AppendLine("-----END PRIVATE KEY-----");

        return sb.ToString();
    }
    private static void AppendBody(StringBuilder sb, string key)
    {
        var count = Convert.ToInt32(Math.Ceiling(key.Length * 1.0 / 64));
        for (int i = 0; i < count; i++)
        {
            var start = i * 64;
            var end = start + 64;
            if (end >= key.Length)
            {
                end = key.Length;
            }
            sb.AppendLine(key[start..end]);
        }
    }

    private static IX509Certificate[] LoadCertificateChainFromBase64(string certificateBase64)
    {
        // Step 1: Decode the Base64 string into a byte array
        var certBytes = Convert.FromBase64String(certificateBase64);

        // Step 2: Parse the certificate using BouncyCastle
        var certParser = new X509CertificateParser();
        var certificate = certParser.ReadCertificate(certBytes);

        // Step 3: Construct the certificate chain
        var certificateChain = new List<IX509Certificate> { new X509CertificateBC(certificate) };

        // Optional: If you have intermediate certificates, you can load and add them to the chain
        // Add each intermediate certificate to the chain (if you have multiple certificates)
        // For example, assuming you have Base64-encoded intermediate certificates:
        // string[] base64IntermediateCerts = { intermediateCert1, intermediateCert2 };
        // foreach (string intermediateCert in base64IntermediateCerts)
        // {
        //     byte[] intermediateCertBytes = Convert.FromBase64String(intermediateCert);
        //     X509Certificate intermediateCertificate = certParser.ReadCertificate(intermediateCertBytes);
        //     certificateChain.Add(intermediateCertificate);
        // }

        // Step 4: Return the certificate chain as an array of IX509Certificate
        return certificateChain.ToArray();
    }
}
